import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  colors,
  ControlledInput,
  UserFilledIcon,
  UserIcon,
  View,
} from '@/components/ui';
import {
  type FormType as AccountSetupFormType,
  useFieldBlurAndFilled,
} from '@/lib';

export default function Name() {
  const { control } = useFormContext<AccountSetupFormType>();
  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<AccountSetupFormType>(['firstName', 'lastName']);

  return (
    <View className="gap-4">
      <ControlledInput
        testID="firstName-input"
        control={control}
        name="firstName"
        label="First name"
        handleFieldBlur={() => handleFieldBlur('firstName')}
        handleFieldUnBlur={() => handleFieldUnBlur('firstName')}
        icon={
          fieldStates.firstName.isFilledAndBlurred ? (
            <UserFilledIcon color={colors.brand['60']} />
          ) : (
            <UserIcon color={colors.brand['60']} />
          )
        }
      />
      <ControlledInput
        testID="lastName-input"
        control={control}
        name="lastName"
        label="Last name"
        handleFieldBlur={() => handleFieldBlur('lastName')}
        handleFieldUnBlur={() => handleFieldUnBlur('lastName')}
        icon={
          fieldStates.lastName.isFilledAndBlurred ? (
            <UserFilledIcon color={colors.brand['60']} />
          ) : (
            <UserIcon color={colors.brand['60']} />
          )
        }
      />
    </View>
  );
}
