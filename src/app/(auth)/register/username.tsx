import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  ActivityIndicator,
  AtIcon,
  colors,
  ControlledInput,
  P,
  View,
} from '@/components/ui';
import {
  type FormType as AccountSetupFormType,
  useAccountSetup,
  useFieldBlurAndFilled,
} from '@/lib';

export default function Username() {
  const { usernameRefinement, isValidatingUsername, setIsValidatingUsername } =
    useAccountSetup();
  const { control, watch } = useFormContext<AccountSetupFormType>();
  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<AccountSetupFormType>(['username']);
  const username = watch('username');

  const UsernameStatusIndicator = () => {
    if (!username) return null;

    if (fieldStates.username.value !== '') {
      return (
        <View className="flex flex-row items-center">
          {isValidatingUsername ? (
            <ActivityIndicator size="small" color={colors.brand['60']} />
          ) : (
            <P>{fieldStates.username.isValid ? '✅' : '❌'}</P>
          )}
          <P className="ml-1 text-grey-50 dark:text-grey-60">
            {isValidatingUsername
              ? 'Checking username'
              : fieldStates.username.isValid
                ? 'Username available'
                : 'Username unavailable'}
          </P>
        </View>
      );
    }

    return null;
  };
  return (
    <>
      <ControlledInput
        testID="username-input"
        control={control}
        name="username"
        label="Username"
        handleFieldBlur={() => handleFieldBlur('username')}
        handleFieldUnBlur={() => handleFieldUnBlur('username')}
        hideErrorMessage={
          fieldStates.username.error === 'Username is already taken'
        }
        onChange={() => {
          setIsValidatingUsername(true);
          usernameRefinement.invalidate();
        }}
        icon={<AtIcon color={colors.brand['60']} />}
      />
      <UsernameStatusIndicator />
    </>
  );
}
