'use client';
import { useRouter } from 'next/navigation';
import { isPast } from 'date-fns';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';
import { OptionType } from '@/types';
import {
  EventType,
  type ExportListType,
  getEventGuestList,
  type GuestListResponse,
  useDeleteEvent,
  useDeleteEventArtist,
  useGetEventDiscounts,
  TicketType,
  useGetEventGuestList,
  useReserveEventTicket,
} from '@/api/events';
import { getQueryClient } from '@/api';
import {
  colors,
  EmptyState,
  H6,
  Modal,
  Small,
  Button,
  H1,
  H4,
  H5,
  SmRegularLabel,
  XsBoldLabel,
  Counter,
  GraphChartIcon,
  semanticColors,
  LockIcon,
  Breadcrumbs,
} from '@/components/ui';
import {
  downloadICS,
  formatAmount,
  toAmountInMajor,
  useAuth,
  hasTicketCategories,
  isPresaleActive,
  useDownloadFile,
  useFavoriteToggle,
  usePurchaseTicketContext,
  cn,
} from '@/lib';
import {
  EventDetailMoreDropdown,
  EventEditModal,
  EventTicketCard,
  EventInfo,
  TicketedEventPage,
  FreeEvent,
} from '@/components/events';
import { ConfirmationDialog } from '@/components/dialogs';
import { useTheme } from 'next-themes';
import { LoadingScreen } from '@/components/loaders';
import { FiEye, FiEyeOff, FiShare } from 'react-icons/fi';
import { EventBannerCarousel } from '@/components/carousels';
import { ShareModal } from '@/components/modals';
import { type PurchaseTicketFormType } from '@/lib/hooks/use-purchase-ticket';
import { useFormContext } from 'react-hook-form';
import { GoHeart, GoHeartFill } from 'react-icons/go';

const exportGuestsModalOptions: OptionType[] = [
  { label: 'PDF', value: 'pdf' },
  { label: 'Excel', value: 'excel' },
];

export const EventDetailsPage = ({ slug }: { slug: string }) => {
  const { user } = useAuth();
  const queryClient = getQueryClient();
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const { control, watch } = useFormContext<PurchaseTicketFormType>();

  const [confirmRAVisible, setConfirmRAVisible] = useState(false);

  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const {
    event,
    eventId,
    categories,
    isEventLoading,
    handleTicketQuantityChange,
    hasSelectedTickets,
    selectedTickets,
    setEditType,
    accessCodeVerified,
    isValidatingAccessCode,
    validateAccessCode,
  } = usePurchaseTicketContext();

  const [currentStep, setCurrentStep] = useState<
    'details' | 'next' | 'success'
  >('details');

  const isCreator = user?.id === (event?.organizer.id || event?.organizerId);
  const isSoldOut = hasTicketCategories(event?.ticketCategories)
    ? Object.values(event.ticketCategories).every((ticketCategory) => {
        const presaleTicket = event?.presaleConfig?.find(
          (presale) => presale.ticketCategoryId === ticketCategory.id
        );

        const shouldUsePresale =
          presaleTicket && isPresaleActive(presaleTicket);

        const effectiveQuantity = shouldUsePresale
          ? presaleTicket.quantity
          : ticketCategory.quantity;

        return effectiveQuantity === 0;
      })
    : false;

  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: event?.isFavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: eventId },
    invalidateQueries: [
      ['getUserFavourites', { type: 'EVENT' }],
      [
        'getEvents',
        {
          userId: user?.id,
          organizerId: event?.organizer.id || event?.organizerId,
        },
      ],
    ],
  });

  const hasEventEnded = event?.endTime ? isPast(event.endTime) : true;

  const isEventRegistrationRequired =
    event?.ticketType === TicketType.FREE && !!event.registrationRequired;

  const hasRegisteredForEvent = Boolean(event?.freeEventRegistration);

  const { mutate: deleteEvent, isPending: deletingEvent } = useDeleteEvent();

  const handleDeleteEvent = () => {
    const confirmed = window.confirm(
      'Are you sure you want to delete this event?'
    );
    if (confirmed) {
      deleteEvent(
        { id: event?.id || eventId },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['getEvents'] });
            toast.success('Event deleted successfully');
            router.back();
          },
          onError: (error: any) => toast.error(error.message),
        }
      );
    }
  };

  const [fileType, setFileType] = useState<ExportListType>();

  const { isFetching: isExportingList } = useGetEventGuestList({
    variables: { eventId, fileType: fileType || 'excel' },
    enabled: false,
  });

  const { downloadFile, isDownloading } = useDownloadFile();

  const handleDownloadGuestList = useCallback(
    async (type: ExportListType) => {
      setFileType(type);

      const queryKey = useGetEventGuestList.getKey({
        eventId,
        fileType: type,
      });

      try {
        const data = await queryClient.fetchQuery<GuestListResponse>({
          queryKey,
          queryFn: () => getEventGuestList({ eventId, fileType: type }),
          staleTime: 5 * 60 * 1000,
        });

        if (data?.link) {
          await downloadFile({
            url: data.link,
            fileName:
              data.link.split('/').pop() || `${event?.title} - guest list`,
            mimeType:
              type === 'excel' ? 'application/vnd.ms-excel' : 'application/pdf',
          });
        }
      } catch (error) {
        console.error('❌ Error downloading guest list:', error);
      } finally {
        setFileType(undefined);
      }
    },
    [eventId, event?.title, queryClient, downloadFile]
  );

  const onSelectOption = useCallback(
    (option: OptionType) => {
      handleDownloadGuestList(option.value as ExportListType);
    },
    [handleDownloadGuestList]
  );

  const eventCategory = categories?.find(
    (categories: any) => categories.id === event?.categoryId
  );
  const hasActivePresale = (): boolean => {
    if (!event?.presaleConfig || event.presaleConfig.length === 0) {
      return false;
    }

    return event.presaleConfig.some((presaleTicket) =>
      isPresaleActive(presaleTicket)
    );
  };

  const [confirmVisible, setConfirmVisible] = useState(false);

  const { data: discounts } = useGetEventDiscounts({
    variables: { eventId },
    enabled: isCreator,
  });

  const router = useRouter();

  const { mutate: removeArtist, isPending: isRemovingArtist } =
    useDeleteEventArtist({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [
            'getEventWithSlug',
            { slug, targetCurrency: 'NGN', userId: user?.id },
          ],
        });
        toast.success('Artist removed from event successfully');
      },
      onError: (error: any) => toast.error(error.message),
    });

  const [editModalVisible, setEditModalVisible] = useState(false);

  const ticketCategories = event?.ticketCategories || {};

  const activeTickets = Object.values(ticketCategories).map(
    (ticketCategory) => {
      const presaleTicket = event?.presaleConfig?.find(
        (presale) => presale.ticketCategoryId === ticketCategory.id
      );

      const shouldUsePresale = presaleTicket && isPresaleActive(presaleTicket);

      return shouldUsePresale
        ? {
            ...ticketCategory,
            cost: presaleTicket.price,
            convertedCost: presaleTicket.price,
            quantity: presaleTicket.quantity,
            purchaseLimit:
              presaleTicket.purchaseLimit || ticketCategory.purchaseLimit,
            description:
              presaleTicket.description || ticketCategory.description,
          }
        : ticketCategory;
    }
  );

  const prices = activeTickets.map(
    (ticket) => ticket.convertedCost || ticket.cost
  );

  const startingPrice = prices.length > 0 ? Math.min(...prices) : null;

  const handleEventEdit = (type: 'desc' | 'date' | 'location') => {
    setEditType(type);
    setEditModalVisible(true);
  };

  const [ticketModalVisible, setTicketModalVisible] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [exportOptionModalVisible, setExportOptionModalVisible] =
    useState(false);

  const [expiresAt, setExpiresAt] = useState<string | null>(null);

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useReserveEventTicket({
      onSuccess: ({ expiresAt }: { expiresAt: string }) => {
        setTicketModalVisible(false);
        setExpiresAt(expiresAt);
        setCurrentStep('next');
      },
      onError: (error: any) => toast.error(error.message),
    });

  if (deletingEvent || isRemovingArtist || isEventLoading)
    return <LoadingScreen />;
  if (!event) return <EmptyState />;

  return (
    <main className="bg-white dark:bg-black dark:text-white text-black md:p-6 p-2">
      {(event?.eventType === EventType.PUBLIC ||
        isCreator ||
        (event?.eventType === EventType.PRIVATE && !!accessCodeVerified)) && (
        <>
          <div className="h-16 flex flex-row items-center justify-between px-2">
            <Breadcrumbs />
            {isCreator ? (
              <div className="flex flex-row gap-2">
                <button
                  className="size-8 cursor-pointer flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
                  onClick={() => router.push(`/events/${slug}/analytics`)}
                >
                  <GraphChartIcon
                    color={
                      isDark
                        ? semanticColors.accent.bold.dark
                        : semanticColors.accent.bold.light
                    }
                  />
                </button>
                <EventDetailMoreDropdown
                  onValueChange={(value) => {
                    switch (value) {
                      case 'share':
                        setShareModalVisible(true);
                        break;
                      case 'discount':
                        router.push(
                          discounts && discounts.length > 0
                            ? `/events/${slug}/discounts`
                            : `/events/${slug}/discounts/create`
                        );
                        break;
                      // case 'message':
                      //   router.push(`/events/${slug}/message-attendees`);
                      //   break;
                      case 'export-guests':
                        setExportOptionModalVisible(true);
                        break;
                      case 'delete':
                        handleDeleteEvent();
                        break;

                      default:
                        break;
                    }
                  }}
                />
              </div>
            ) : (
              <div className="flex flex-row gap-2">
                <button
                  className="size-8 flex items-center justify-center rounded-full bg-brand-200 dark:bg-brand-900 cursor-pointer"
                  onClick={handleFavToggle}
                >
                  {favourited ? (
                    <GoHeartFill size={24} color={colors.brand[50]} />
                  ) : (
                    <GoHeart size={24} color={colors.brand[60]} />
                  )}
                </button>
                <button
                  onClick={() => setShareModalVisible(true)}
                  className="cursor-pointer"
                >
                  <FiShare size={24} color={colors.brand[60]} />
                </button>
              </div>
            )}
          </div>

          <div className="grid sm:grid-cols-1 md:grid-cols-2">
            <div className="px-0 md:px-4">
              <EventBannerCarousel
                event={event}
                eventCategory={eventCategory!}
                isCreator={isCreator}
              />
            </div>

            {currentStep === 'details' && (
              <div className="px-4 py-6 flex flex-col gap-y-4">
                <div className="flex flex-row items-center justify-between">
                  <H1>{event.title}</H1>
                  {isCreator ? (
                    <div
                      className={cn(
                        'p-2 flex flex-row items-center justify-center rounded-full',
                        event?.eventType === 'PRIVATE'
                          ? 'bg-bg-error-light dark:bg-bg-error-dark'
                          : 'bg-bg-success-light dark:bg-bg-success-dark'
                      )}
                    >
                      {event?.eventType === 'PRIVATE' ? (
                        <FiEyeOff
                          size={12}
                          color={
                            isDark
                              ? semanticColors.fg.error.dark
                              : semanticColors.fg.error.light
                          }
                          className="mr-1"
                        />
                      ) : (
                        <FiEye
                          size={12}
                          color={
                            isDark
                              ? semanticColors.fg.success.dark
                              : semanticColors.fg.success.light
                          }
                          className="mr-1"
                        />
                      )}
                      <XsBoldLabel
                        className={cn(
                          event?.eventType === 'PRIVATE'
                            ? 'text-fg-error-light dark:text-fg-error-dark'
                            : 'text-green-60 dark:text-green-40'
                        )}
                      >
                        {event?.eventType === 'PRIVATE' ? 'Private' : 'Public'}
                      </XsBoldLabel>
                    </div>
                  ) : (
                    <div className="flex flex-row items-center gap-2">
                      {hasActivePresale() && (
                        <span className="bg-bg-success-light dark:bg-bg-success-dark px-2 py-1 rounded-full flex justify-center items-center">
                          <XsBoldLabel className="text-fg-success-light dark:text-fg-success-dark">
                            Presale
                          </XsBoldLabel>
                        </span>
                      )}
                      {hasEventEnded && (
                        <span className="bg-bg-danger-primary dark:bg-bg-danger-primary px-2 py-1 rounded-full flex justify-center items-center">
                          <XsBoldLabel className="text-fg-danger-primary dark:text-fg-danger-primary">
                            Ended
                          </XsBoldLabel>
                        </span>
                      )}
                    </div>
                  )}
                </div>

                <EventInfo
                  addToCalendar={() =>
                    downloadICS({
                      title: event.title,
                      description: event.description,
                      location: event.location.address,
                      start: new Date(event.startTime),
                      end: new Date(event.endTime),
                    })
                  }
                  event={event}
                  isCreator={isCreator}
                  openInMaps={() => {
                    const query = encodeURIComponent(event.location.address);
                    window.open(
                      `https://www.google.com/maps/search/?api=1&query=${query}`,
                      '_blank'
                    );
                  }}
                  handleEventEdit={handleEventEdit}
                  setConfirmRAVisible={setConfirmRAVisible}
                  setArtistToDelete={setArtistToDelete}
                />

                {isCreator &&
                  event?.ticketCategories &&
                  Object.entries(event.ticketCategories).length > 0 && (
                    <div className="flex flex-col gap-y-[10px]">
                      <H5 className="text-fg-muted-light dark:text-fg-muted-dark">
                        Tickets
                      </H5>
                      {Object.entries(event.ticketCategories).map(
                        ([key, ticket], index) => (
                          <div key={key} className="flex flex-col gap-y-4">
                            <EventTicketCard
                              ticket={{
                                ...ticket,
                                cost: toAmountInMajor(ticket.cost),
                              }}
                              category={key}
                              index={index}
                              isDark={isDark}
                              onEdit={() => {
                                router.push(
                                  `/events/${eventId}/edit-ticket?category=${encodeURIComponent(
                                    key
                                  )}&ticketCategoryId=${encodeURIComponent(ticket.id)}`
                                );
                              }}
                              onDelete={() => {}}
                              disabled={true}
                            />
                          </div>
                        )
                      )}
                      <Button
                        label="Add new ticket +"
                        variant="secondary"
                        className="w-[154px] py-2"
                        size="sm"
                        onPress={() =>
                          router.push(`/events/${event.slug}/add-ticket`)
                        }
                      />
                    </div>
                  )}

                {isCreator ? (
                  <div className="px-4">
                    <Button
                      label="Scan ticket"
                      onPress={() =>
                        router.push(`/events/${event.slug}/scan-ticket`)
                      }
                      disabled={hasEventEnded}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-between md:py-4 md:px-4 py-4 border-t">
                    <div>
                      {hasTicketCategories(event?.ticketCategories) ? (
                        <>
                          <H4>
                            {formatAmount(
                              Number(toAmountInMajor(startingPrice || 0))
                            )}
                          </H4>
                          <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                            Starting from
                          </SmRegularLabel>
                        </>
                      ) : (
                        <H4>Free</H4>
                      )}
                    </div>
                    <Button
                      label={
                        hasEventEnded
                          ? 'Expired'
                          : isSoldOut
                            ? 'Sold out'
                            : isEventRegistrationRequired
                              ? hasRegisteredForEvent
                                ? 'Registered'
                                : 'Register'
                              : 'Get Ticket'
                      }
                      className="w-[256px]"
                      onPress={() => {
                        if (isEventRegistrationRequired) {
                          setCurrentStep('next');
                        } else {
                          setTicketModalVisible(true);
                        }
                      }}
                      disabled={
                        isCreator ||
                        hasEventEnded ||
                        isSoldOut ||
                        hasRegisteredForEvent
                      }
                    />
                  </div>
                )}
              </div>
            )}
            {currentStep === 'next' ? (
              event.isTicketed ? (
                <TicketedEventPage
                  expiresAt={expiresAt ?? null}
                  setLayoutCurrentStep={setCurrentStep}
                />
              ) : (
                <FreeEvent />
              )
            ) : null}
          </div>
          <Modal
            isOpen={ticketModalVisible}
            onClose={() => setTicketModalVisible(false)}
            title="Add tickets"
          >
            <div className="flex flex-col gap-2">
              <div>
                {Object.entries(ticketCategories)
                  .map(([categoryKey, ticketCategory]) => {
                    const presaleTicket = event?.presaleConfig?.find(
                      (presale) =>
                        presale.ticketCategoryId === ticketCategory.id
                    );

                    const shouldUsePresale =
                      presaleTicket && isPresaleActive(presaleTicket);

                    const activeTicket = shouldUsePresale
                      ? {
                          ...ticketCategory,
                          cost: presaleTicket.price,
                          quantity: presaleTicket.quantity,
                          purchaseLimit:
                            presaleTicket.purchaseLimit ||
                            ticketCategory.purchaseLimit,
                          description:
                            presaleTicket.description ||
                            ticketCategory.description,
                          availableTickets: presaleTicket.availableTickets,
                        }
                      : {
                          ...ticketCategory,
                          availableTickets: ticketCategory.quantity,
                        };

                    return { categoryKey, activeTicket, shouldUsePresale };
                  })
                  .sort((a, b) => {
                    const costA =
                      a.activeTicket.convertedCost || a.activeTicket.cost;
                    const costB =
                      b.activeTicket.convertedCost || b.activeTicket.cost;
                    return costA - costB;
                  })
                  .map(
                    (
                      { categoryKey, activeTicket, shouldUsePresale },
                      index
                    ) => (
                      <div
                        key={index}
                        className="flex flex-row items-start gap-6 py-4"
                      >
                        <div className="flex-1 flex-col gap-2">
                          <div className="flex flex-row items-center justify-between">
                            <H4>{categoryKey}</H4>
                            {shouldUsePresale && (
                              <div className="h-5 w-[58px] flex items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                                <XsBoldLabel className="text-green-60 dark:text-green-40">
                                  Presale
                                </XsBoldLabel>
                              </div>
                            )}
                          </div>
                          <H6 className="text-fg-muted-light dark:text-fg-muted-dark">
                            {formatAmount(
                              Number(
                                toAmountInMajor(
                                  activeTicket.convertedCost ||
                                    activeTicket.cost
                                )
                              )
                            )}
                          </H6>
                          <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                            {activeTicket.description}
                          </Small>
                        </div>
                        <Counter
                          initialValue={selectedTickets[categoryKey] || 0}
                          value={selectedTickets[categoryKey] || 0}
                          minimum={0}
                          maximum={
                            (activeTicket.availableTickets ||
                              activeTicket.quantity) > 10
                              ? 10
                              : activeTicket.availableTickets
                          }
                          onValueChange={(quantity) =>
                            handleTicketQuantityChange(categoryKey, quantity)
                          }
                          className="border border-border-subtle-light dark:border-border-subtle-dark"
                        />
                      </div>
                    )
                  )}
              </div>
              <Button
                label="Continue"
                disabled={!hasSelectedTickets || reservingTicket}
                loading={reservingTicket}
                onPress={() => {
                  reserveTicket({
                    id: event?.id || eventId,
                    reservations: Object.entries(selectedTickets)
                      .filter(([, quantity]) => quantity !== 0)
                      .map(([selectedTicketCategory, quantity]) => ({
                        category: selectedTicketCategory,
                        quantity,
                      })),
                  });
                  setTicketModalVisible(false);
                  setCurrentStep('next');
                }}
              />
            </div>
          </Modal>
          <ConfirmationDialog
            visible={confirmRAVisible}
            message="Are you sure you want to delete this artist?"
            onCancel={() => {
              setConfirmRAVisible(false);
              setArtistToDelete(null);
            }}
            onConfirm={() => {
              if (artistToDelete !== null) {
                removeArtist({
                  id: event?.id || eventId,
                  artistId: artistToDelete,
                });
              }
              setConfirmRAVisible(false);
              setArtistToDelete(null);
            }}
          />
          <ConfirmationDialog
            visible={confirmVisible}
            message="Are you sure you want to remove this event from your favourites?"
            onCancel={() => setConfirmVisible(false)}
            onConfirm={() => {
              setConfirmVisible(false);
              handleFavToggle();
            }}
          />
          <ShareModal
            isOpen={shareModalVisible}
            onDismiss={() => setShareModalVisible(false)}
            content={`${process.env.APP_URL}/events/${event?.slug || slug}`}
          />
          {isCreator && event && (
            <>
              <EventEditModal
                event={event}
                isOpen={editModalVisible}
                onClose={() => setEditModalVisible(false)}
              />
              <Modal
                isOpen={exportOptionModalVisible}
                onClose={() => setExportOptionModalVisible(false)}
                title="Select export format"
              >
                <div className="flex flex-col gap-4 pb-4">
                  {exportGuestsModalOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => onSelectOption(option)}
                      className={`w-full rounded-md border px-4 py-2 text-left transition cursor-pointer ${
                        fileType === option.value
                          ? 'border-accent-moderate bg-accent-moderate/10'
                          : 'border-gray-300 dark:border-gray-600'
                      }`}
                      disabled={isExportingList || isDownloading}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>

                <Button
                  label="Done"
                  onPress={() => setExportOptionModalVisible(false)}
                  loading={isExportingList || isDownloading}
                />
              </Modal>
            </>
          )}
        </>
      )}
      <ConfirmationDialog
        backdropClassName="bg-black"
        visible={
          !isCreator &&
          event?.eventType === EventType.PRIVATE &&
          !accessCodeVerified
        }
        title="Event code"
        message="Enter private event code below"
        control={control}
        inputLabel="Access Code"
        inputName="accessCode"
        inputIcon={
          <LockIcon color={isDark ? colors.grey[60] : colors.grey[50]} />
        }
        onCancel={() => router.push('/events')}
        confirmLabel="Continue"
        onConfirm={() =>
          validateAccessCode({ eventId, code: watch('accessCode') || '' })
        }
        isConfirming={isValidatingAccessCode}
        btnDisabled={!watch('accessCode')}
      />
    </main>
  );
};
