import React from 'react';

export const ActivityIndicator: React.FC<{ size?: number; color?: string }> = ({
  size = 24,
  color = '#333',
}) => {
  return (
    <div
      style={{
        width: size,
        height: size,
        border: `${size / 8}px solid #f3f3f3`,
        borderTop: `${size / 8}px solid ${color}`,
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
      }}
    />
  );
};

const styles = `
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
`;

if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}
